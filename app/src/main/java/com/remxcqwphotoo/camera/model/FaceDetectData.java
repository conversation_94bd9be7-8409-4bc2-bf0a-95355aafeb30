package com.remxcqwphotoo.camera.model;

import java.util.List;

/**
 * 新的人脸检测API响应数据模型
 * API: https://api.juyingnj.com/face/detect
 */
public class FaceDetectData {
    /**
     * 检测到的人脸数量
     */
    public int face_count;
    
    /**
     * 人脸位置信息列表
     */
    public List<FaceLocation> face_locations;
    
    /**
     * 处理时间（秒）
     */
    public double processing_time;
    
    /**
     * 人脸位置信息
     */
    public static class FaceLocation {
        /**
         * 人脸区域上边界
         */
        public int top;
        
        /**
         * 人脸区域右边界
         */
        public int right;
        
        /**
         * 人脸区域下边界
         */
        public int bottom;
        
        /**
         * 人脸区域左边界
         */
        public int left;
        
        /**
         * 获取人脸区域宽度
         */
        public int getWidth() {
            return right - left;
        }
        
        /**
         * 获取人脸区域高度
         */
        public int getHeight() {
            return bottom - top;
        }
        
        /**
         * 获取人脸中心点X坐标
         */
        public int getCenterX() {
            return left + getWidth() / 2;
        }
        
        /**
         * 获取人脸中心点Y坐标
         */
        public int getCenterY() {
            return top + getHeight() / 2;
        }
        
        /**
         * 检查人脸区域是否有效
         */
        public boolean isValid() {
            return right > left && bottom > top && left >= 0 && top >= 0;
        }
    }
}
